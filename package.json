{"name": "realtime-mcp-proxy", "version": "0.1.0", "description": "A library that bridges OpenAI's Realtime API with Model Context Protocol (MCP) servers, enabling real-time voice-driven tool execution", "private": true, "type": "module", "workspaces": ["packages/*", "examples/voice-demo"], "scripts": {"build": "turbo run build", "dev": "turbo run dev", "test": "turbo run test", "test:coverage": "turbo run test:coverage", "lint": "turbo run lint", "type-check": "turbo run type-check", "clean": "turbo run clean", "changeset": "changeset", "version-packages": "changeset version", "release": "turbo run build && changeset publish"}, "devDependencies": {"@changesets/cli": "^2.27.1", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.4.1", "prettier": "^3.2.5", "turbo": "^1.12.4", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}, "keywords": ["openai", "realtime-api", "mcp", "model-context-protocol", "voice-ai", "speech-to-speech", "function-calling", "typescript"], "author": "Realtime MCP Community", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/Gillinghammer/realtime-to-mcp.git"}, "bugs": {"url": "https://github.com/Gillinghammer/realtime-to-mcp/issues"}, "homepage": "https://github.com/Gillinghammer/realtime-to-mcp#readme", "dependencies": {"@gillinghammer/realtime-mcp-core": "^0.1.0"}}