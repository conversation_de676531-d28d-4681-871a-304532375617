{"name": "@gillinghammer/realtime-mcp-core", "version": "0.1.0", "description": "Core library for bridging OpenAI's Realtime API with MCP servers", "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}}, "files": ["dist", "README.md"], "scripts": {"build": "tsup", "dev": "tsup --watch", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "lint": "eslint src --ext .ts,.tsx", "type-check": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"cors": "^2.8.5", "express": "^4.18.2", "ws": "^8.16.0", "zod": "^3.22.4"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^20.11.17", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^8.34.0", "@typescript-eslint/parser": "^8.34.0", "@vitest/coverage-v8": "^1.2.2", "eslint": "^8.56.0", "execa": "^8.0.1", "rimraf": "^5.0.5", "tsup": "^8.0.2", "typescript": "^5.3.3", "vitest": "^1.2.2"}, "engines": {"node": ">=18.0.0"}, "keywords": ["openai", "realtime", "mcp", "voice-ai", "typescript"], "author": "Realtime MCP Community", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/Gillinghammer/realtime-to-mcp.git", "directory": "packages/core"}}