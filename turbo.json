{"$schema": "https://turbo.build/schema.json", "pipeline": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": ["dist/**", ".next/**", "!.next/cache/**"]}, "dev": {"cache": false, "persistent": true}, "test": {"dependsOn": ["build"], "inputs": ["$TURBO_DEFAULT$", "src/**/*.test.*", "test/**/*"], "outputs": ["coverage/**"]}, "test:coverage": {"dependsOn": ["build"], "inputs": ["$TURBO_DEFAULT$", "src/**/*.test.*", "test/**/*"], "outputs": ["coverage/**"]}, "lint": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".eslintrc*", "eslint.config.*"]}, "type-check": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", "tsconfig*.json"]}, "clean": {"cache": false}}, "globalDependencies": ["**/.env*"], "globalEnv": ["NODE_ENV"]}