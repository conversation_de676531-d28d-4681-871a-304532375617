{"name": "realtime-mcp-voice-demo", "version": "0.1.0", "description": "Unified voice interface for multiple MCP servers - HubSpot, HackerNews, Airbnb, Blender, Fewsats, Amazon, and more", "type": "module", "scripts": {"dev": "tsx server.ts", "start": "tsx server.ts", "setup": "cp .env.example .env && echo 'Please edit .env with your API keys'"}, "dependencies": {"@gillinghammer/realtime-mcp-core": "^0.1.0", "express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "ws": "^8.16.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/node": "^20.10.0", "@types/ws": "^8.5.10", "tsx": "^4.6.2", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}}