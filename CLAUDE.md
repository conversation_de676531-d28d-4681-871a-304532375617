# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a monorepo for the Realtime MCP Proxy - a TypeScript library that bridges OpenAI's Realtime API with Model Context Protocol (MCP) servers, enabling voice-driven tool execution. The project uses Turbo for monorepo management and includes:

- `packages/core/` - Core library (`@gillinghammer/realtime-mcp-core`)
- `examples/voice-demo/` - Unified voice interface demo for multiple MCP servers

## Common Development Commands

### Build and Development
```bash
# Build all packages
npm run build

# Start development mode (watches for changes)
npm run dev

# Run tests across all packages
npm run test

# Run tests with coverage
npm run test:coverage

# Lint all packages
npm run lint

# Type checking
npm run type-check

# Clean build artifacts
npm run clean
```

### Package-specific Commands
```bash
# Core package development
cd packages/core
npm run dev          # Watch mode build
npm run test:watch   # Watch mode testing
npm run build        # Build for production

# Voice demo example
cd examples/voice-demo
npm run dev          # Start demo server
npm run setup        # Copy .env.example to .env
```

## Architecture

### Core Components

1. **WebRTCBridgeServer** (`packages/core/src/webrtc/bridge-server.ts`)
   - Main entry point for users
   - Provides HTTP endpoints for WebRTC integration
   - Manages MCP server processes via stdio
   - Handles ephemeral API key generation

2. **RealtimeMCPProxy** (`packages/core/src/proxy.ts`)
   - Advanced proxy class for custom implementations
   - Bridges OpenAI Realtime API with MCP servers
   - Handles real-time event processing and function calls

3. **MCPClient** (`packages/core/src/mcp/client.ts`)
   - HTTP-based MCP client for external servers
   - Handles tool discovery and execution
   - Supports bearer token authentication

4. **StdioMCPClient** (in `bridge-server.ts`)
   - Stdio-based MCP client for spawned processes
   - Manages process lifecycle and communication
   - Handles JSON-RPC message parsing

### Key Endpoints (WebRTCBridgeServer)
- `GET /session` - Ephemeral API keys for WebRTC
- `POST /mcp` - MCP proxy for tool calls
- `GET /tools` - OpenAI-formatted tool definitions
- `GET /demo` - Live demo page
- `GET /health` - Health check and status

### Configuration Types

The system uses Zod schemas for configuration validation:
- `WebRTCBridgeConfig` - Main configuration interface
- `OpenAIConfig` - OpenAI API settings (key, model, voice, instructions)
- `MCPConfig` - MCP server configuration (URL or command/args for stdio)
- `AuthConfig` - Authentication for MCP servers

### MCP Integration Patterns

1. **Stdio Process**: Spawn MCP servers as child processes
   ```typescript
   mcp: {
     command: 'npx',
     args: ['-y', '@hubspot/mcp-server'],
     env: { PRIVATE_APP_ACCESS_TOKEN: token }
   }
   ```

2. **HTTP Server**: Connect to existing MCP servers
   ```typescript
   mcp: {
     url: 'http://localhost:3000',
     auth: { type: 'bearer', token: 'token' }
   }
   ```

## Development Notes

- Uses TypeScript with strict type checking
- ESM modules throughout (`.js` imports required)
- Vitest for testing with coverage support
- ESLint + Prettier for code formatting
- Node.js 18+ required
- Turbo for monorepo task orchestration

## Testing

Tests are located alongside source files (`.test.ts` suffix). The project uses:
- Vitest for unit testing
- Coverage reports via `@vitest/coverage-v8`
- Test files follow the pattern `*.test.ts`

Run `npm run test` from root to test all packages, or `npm run test:watch` in individual packages for development.